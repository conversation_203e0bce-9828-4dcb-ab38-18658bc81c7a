module.exports = function (api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      'react-native-reanimated/plugin',
      [
        'module-resolver',
        {
          root: ['./'],
          alias: {
            '@': './',
            '@components': './components',
            '@screens': './Screens',
            '@services': './services',
            '@utils': './utils',
            '@types': './types',
            '@constants': './constants',
          },
        },
      ],
    ],
  };
};
