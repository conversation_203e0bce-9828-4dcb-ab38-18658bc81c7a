# Mobile App Project

A beautiful React Native mobile application built with Expo, featuring a modern design and smooth navigation.

## Features

- **Beautiful UI Design**: Modern, clean interface with consistent design system
- **Tab Navigation**: Easy navigation between different screens
- **Responsive Layout**: Works great on different screen sizes
- **Reusable Components**: Well-structured component library
- **TypeScript Support**: Full TypeScript integration for better development experience

## Screens

### 🏠 Home Screen
- Welcome message with user greeting
- Search functionality
- Category browsing
- Featured destinations
- Recent bookings overview

### 🔍 Explore Screen
- Talent discovery interface
- Advanced filtering options
- Search functionality
- Professional profiles with ratings

### 📅 Bookings Screen
- Booking management
- Status tracking (Pending, Confirmed, Completed)
- Contact options
- Booking history

### 💬 Messages Screen
- Conversation list
- Unread message indicators
- Clean messaging interface

### 👤 Profile Screen
- User profile management
- Statistics display
- Quick action buttons
- Professional information

## Project Structure

```
├── App.tsx                 # Main app entry point with navigation
├── Screens/               # All screen components
│   ├── HomeScreen.tsx
│   ├── ExploreScreen.tsx
│   ├── BookingsScreen.tsx
│   ├── MessageScreen.tsx
│   └── ProfileScreen.tsx
├── components/            # Reusable UI components
│   ├── Button.tsx
│   ├── Card.tsx
│   ├── Input.tsx
│   └── LoadingSpinner.tsx
├── constants/             # Design system constants
│   ├── Colors.ts
│   ├── Typography.ts
│   └── Spacing.ts
├── types/                 # TypeScript type definitions
├── utils/                 # Helper functions
└── assets/               # Images and other assets
```

## Dependencies

- **React Native**: Mobile app framework
- **Expo**: Development platform
- **React Navigation**: Navigation library
- **Lucide React Native**: Beautiful icons
- **TypeScript**: Type safety

## Getting Started

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Start the development server**:
   ```bash
   npm start
   ```

3. **Run on device**:
   - Install Expo Go app on your phone
   - Scan the QR code from the terminal
   - Or use `npm run android` / `npm run ios` for simulators

## Design System

The app uses a consistent design system with:

- **Colors**: Primary (Indigo), Secondary (Sky), Neutral grays, Status colors
- **Typography**: Consistent font sizes, weights, and line heights
- **Spacing**: 4px base unit with consistent spacing scale
- **Components**: Reusable UI components with consistent styling

## Development

The app is built with modern React Native practices:

- Functional components with hooks
- TypeScript for type safety
- Consistent code structure
- Reusable component library
- Clean separation of concerns

## Contributing

1. Follow the existing code structure
2. Use TypeScript for all new components
3. Follow the design system guidelines
4. Test on both iOS and Android

## License

This project is licensed under the 0BSD License.
