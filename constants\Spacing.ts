export const Spacing = {
  // Base spacing unit (4px)
  unit: 4,
  
  // Spacing scale
  xs: 4,    // 4px
  sm: 8,    // 8px
  md: 12,   // 12px
  lg: 16,   // 16px
  xl: 20,   // 20px
  '2xl': 24, // 24px
  '3xl': 32, // 32px
  '4xl': 40, // 40px
  '5xl': 48, // 48px
  '6xl': 64, // 64px
  '7xl': 80, // 80px
  '8xl': 96, // 96px
};

export const Layout = {
  // Container padding
  containerPadding: Spacing.lg,
  
  // Section spacing
  sectionSpacing: Spacing['3xl'],
  
  // Card spacing
  cardPadding: Spacing.lg,
  cardMargin: Spacing.sm,
  
  // Button spacing
  buttonPadding: {
    small: { horizontal: Spacing.md, vertical: Spacing.sm },
    medium: { horizontal: Spacing.lg, vertical: Spacing.md },
    large: { horizontal: Spacing.xl, vertical: Spacing.lg },
  },
  
  // Input spacing
  inputPadding: { horizontal: Spacing.lg, vertical: Spacing.md },
  
  // Border radius
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    '2xl': 20,
    full: 9999,
  },
  
  // Shadow
  shadow: {
    small: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    medium: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    large: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 4,
    },
  },
};
