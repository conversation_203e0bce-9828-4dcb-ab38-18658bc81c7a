import React, { useState, useMemo, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface Talent {
  id: string;
  name: string;
  skill: string;
  rate: string;
  rating: number;
  reviews: number;
  distanceKm: number;
}

const MOCK_TALENT: Talent[] = [
  { id: '1', name: '<PERSON>', skill: 'Web Designer', rate: '$40/hr', rating: 4.8, reviews: 32, distanceKm: 1.2 },
  { id: '2', name: '<PERSON>', skill: 'Photographer', rate: '$60/hr', rating: 4.6, reviews: 21, distanceKm: 3.5 },
  { id: '3', name: '<PERSON>', skill: 'Mobile Developer', rate: '$50/hr', rating: 4.9, reviews: 44, distanceKm: 2.1 },
  { id: '4', name: '<PERSON>', skill: 'Copywriter', rate: '$35/hr', rating: 4.7, reviews: 18, distanceKm: 0.8 },
];

const FILTERS = ['All', 'Design', 'Dev', 'Photo', 'Writing', 'Video'];

export default function ExploreScreen() {
  const [query, setQuery] = useState('');
  const [activeFilter, setActiveFilter] = useState('All');

  const filtered: Talent[] = useMemo(() => {
    const lower = query.trim().toLowerCase();
    return MOCK_TALENT.filter((t) => {
      const matchesText = !lower || t.name.toLowerCase().includes(lower) || t.skill.toLowerCase().includes(lower);
      const matchesFilter =
        activeFilter === 'All' ||
        (activeFilter === 'Design' && /design/i.test(t.skill)) ||
        (activeFilter === 'Dev' && /(mobile|web|developer)/i.test(t.skill)) ||
        (activeFilter === 'Photo' && /photo/i.test(t.skill)) ||
        (activeFilter === 'Writing' && /(copy|write)/i.test(t.skill)) ||
        (activeFilter === 'Video' && /video/i.test(t.skill));
      return matchesText && matchesFilter;
    });
  }, [query, activeFilter]);

  const renderItem = useCallback(({ item }: { item: Talent }) => {
    return (
      <View style={styles.card}>
        <View style={styles.cardHeader}>
          <Ionicons name="person-circle" size={56} color="#9CA3AF" />
          <View style={{ marginLeft: 12, flex: 1 }}>
            <Text style={styles.name}>{item.name}</Text>
            <Text style={styles.skill}>{item.skill}</Text>
            <View style={styles.metaRow}>
              <Ionicons name="star" size={14} color="#F59E0B" />
              <Text style={styles.metaText}>{item.rating.toFixed(1)} · {item.reviews} reviews</Text>
              <Ionicons name="location" size={14} color="#60A5FA" style={{ marginLeft: 8 }} />
              <Text style={styles.metaText}>{item.distanceKm.toFixed(1)} km</Text>
            </View>
          </View>
          <Text style={styles.rate}>{item.rate}</Text>
        </View>
        <View style={styles.cardActions}>
          <TouchableOpacity style={[styles.chip, { backgroundColor: '#E0F2FE' }]}>
            <Ionicons name="calendar" size={16} color="#0284C7" />
            <Text style={[styles.chipText, { color: '#0369A1' }]}>Availability</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.primaryBtn}>
            <Text style={styles.primaryBtnText}>Request</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }, []);

  return (
    <View style={styles.container}>
      <View style={styles.hero}>
        <View style={styles.heroRow}>
          <View>
            <Text style={styles.heroTitle}>Find Local Talent</Text>
            <Text style={styles.heroSubtitle}>Hire coders, designers, photographers and more</Text>
          </View>
          <View style={styles.notifBtn}>
            <Ionicons name="notifications-outline" size={22} color="#0EA5E9" />
          </View>
        </View>
        <View style={styles.searchWrap}>
          <Ionicons name="search" size={20} color="#64748B" />
          <TextInput
            placeholder="Search talent, skill…"
            placeholderTextColor="#94A3B8"
            style={styles.input}
            value={query}
            onChangeText={setQuery}
            returnKeyType="search"
          />
          <TouchableOpacity style={styles.filterBtn}>
            <Ionicons name="options-outline" size={18} color="#0EA5E9" />
          </TouchableOpacity>
        </View>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={{ paddingRight: 8 }}>
          {FILTERS.map((f) => {
            const active = f === activeFilter;
            return (
              <TouchableOpacity key={f} onPress={() => setActiveFilter(f)} style={[styles.filterChip, active && styles.filterChipActive]}>
                <Text style={[styles.filterChipText, active && styles.filterChipTextActive]}>{f}</Text>
              </TouchableOpacity>
            );
          })}
        </ScrollView>
      </View>

      <FlatList
        data={filtered}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={{ padding: 16, paddingTop: 8, paddingBottom: 32 }}
        ListEmptyComponent={() => (
          <View style={styles.empty}>
            <Ionicons name="search" size={28} color="#94A3B8" />
            <Text style={styles.emptyText}>No results. Try a different search.</Text>
          </View>
        )}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  hero: {
    paddingTop: 8,
    paddingHorizontal: 16,
  },
  heroRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 8,
    marginBottom: 8,
  },
  heroTitle: {
    fontSize: 22,
    fontWeight: '700',
    color: '#0F172A',
  },
  heroSubtitle: {
    color: '#64748B',
    marginTop: 4,
  },
  notifBtn: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E0F2FE',
    alignItems: 'center',
    justifyContent: 'center',
  },
  searchWrap: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 12,
    paddingHorizontal: 12,
    marginTop: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOpacity: 0.06,
    shadowRadius: 6,
    shadowOffset: { width: 0, height: 2 },
  },
  input: {
    flex: 1,
    paddingVertical: 12,
    marginLeft: 8,
    color: '#0F172A',
  },
  filterBtn: {
    width: 36,
    height: 36,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F0F9FF',
  },
  filterChip: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 999,
    backgroundColor: '#E5E7EB',
    marginRight: 8,
    marginTop: 12,
  },
  filterChipActive: {
    backgroundColor: '#0EA5E9',
  },
  filterChipText: {
    color: '#334155',
    fontWeight: '600',
  },
  filterChipTextActive: {
    color: 'white',
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 14,
    padding: 14,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOpacity: 0.06,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 3 },
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  name: { fontSize: 16, fontWeight: '700', color: '#0F172A' },
  skill: { color: '#64748B', marginTop: 2 },
  rate: { fontWeight: '700', color: '#0EA5E9' },
  metaRow: { flexDirection: 'row', alignItems: 'center', marginTop: 6 },
  metaText: { color: '#64748B', marginLeft: 4, fontSize: 12 },
  cardActions: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 12,
  },
  chip: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6 as any,
    paddingHorizontal: 10,
    paddingVertical: 8,
    borderRadius: 10,
  },
  chipText: { fontWeight: '600' },
  primaryBtn: {
    backgroundColor: '#0EA5E9',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 10,
  },
  primaryBtnText: { color: 'white', fontWeight: '700' },
  empty: { alignItems: 'center', padding: 24 },
  emptyText: { color: '#94A3B8', marginTop: 8 },
});