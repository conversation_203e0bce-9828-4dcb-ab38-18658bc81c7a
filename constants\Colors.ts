export const Colors = {
  // Primary colors
  primary: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9',
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e',
  },
  
  // Secondary colors (Indigo)
  secondary: {
    50: '#eef2ff',
    100: '#e0e7ff',
    200: '#c7d2fe',
    300: '#a5b4fc',
    400: '#818cf8',
    500: '#6366f1',
    600: '#4f46e5',
    700: '#4338ca',
    800: '#3730a3',
    900: '#312e81',
  },
  
  // Neutral colors
  neutral: {
    50: '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#64748b',
    600: '#475569',
    700: '#334155',
    800: '#1e293b',
    900: '#0f172a',
  },
  
  // Status colors
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
  },
  
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
  },
  
  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
  },
  
  // Common colors
  white: '#ffffff',
  black: '#000000',
  transparent: 'transparent',
};

export const ThemeColors = {
  light: {
    background: Colors.neutral[50],
    surface: Colors.white,
    primary: Colors.secondary[500],
    secondary: Colors.primary[500],
    text: Colors.neutral[900],
    textSecondary: Colors.neutral[600],
    border: Colors.neutral[200],
    success: Colors.success[500],
    warning: Colors.warning[500],
    error: Colors.error[500],
  },
  dark: {
    background: Colors.neutral[900],
    surface: Colors.neutral[800],
    primary: Colors.secondary[400],
    secondary: Colors.primary[400],
    text: Colors.neutral[50],
    textSecondary: Colors.neutral[400],
    border: Colors.neutral[700],
    success: Colors.success[400],
    warning: Colors.warning[400],
    error: Colors.error[400],
  },
};
