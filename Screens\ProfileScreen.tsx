import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

export default function ProfileScreen() {
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.avatarWrap}>
          <Ionicons name="person-circle" size={76} color="#93C5FD" />
        </View>
        <View style={{ marginLeft: 12, flex: 1 }}>
          <Text style={styles.name}>You</Text>
          <Text style={styles.role}>Freelancer · Designer & No-code</Text>
        </View>
        <TouchableOpacity style={styles.editBtn}>
          <Ionicons name="create-outline" size={18} color="#0EA5E9" />
          <Text style={styles.editText}>Edit</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.statsRow}>
        <View style={styles.statBox}>
          <Text style={styles.statNum}>4.8</Text>
          <Text style={styles.statLbl}>Rating</Text>
        </View>
        <View style={styles.statBox}>
          <Text style={styles.statNum}>57</Text>
          <Text style={styles.statLbl}>Jobs</Text>
        </View>
        <View style={styles.statBox}>
          <Text style={styles.statNum}>$45/hr</Text>
          <Text style={styles.statLbl}>Rate</Text>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        <View style={styles.actionsGrid}>
          <TouchableOpacity style={styles.actionCard}>
            <Ionicons name="briefcase-outline" size={22} color="#0EA5E9" />
            <Text style={styles.actionText}>My Listings</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionCard}>
            <Ionicons name="image-outline" size={22} color="#0EA5E9" />
            <Text style={styles.actionText}>Portfolio</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionCard}>
            <Ionicons name="card-outline" size={22} color="#0EA5E9" />
            <Text style={styles.actionText}>Payouts</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionCard}>
            <Ionicons name="notifications-outline" size={22} color="#0EA5E9" />
            <Text style={styles.actionText}>Notifications</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  header: { flexDirection: 'row', alignItems: 'center', paddingHorizontal: 16, paddingTop: 12 },
  avatarWrap: { width: 76, height: 76, borderRadius: 38, alignItems: 'center', justifyContent: 'center', backgroundColor: '#EFF6FF' },
  name: { fontSize: 20, fontWeight: '800', color: '#0F172A' },
  role: { color: '#64748B', marginTop: 4 },
  editBtn: { flexDirection: 'row', alignItems: 'center', backgroundColor: '#E0F2FE', paddingHorizontal: 12, paddingVertical: 8, borderRadius: 10 },
  editText: { color: '#0369A1', fontWeight: '700', marginLeft: 6 },
  statsRow: { flexDirection: 'row', justifyContent: 'space-between', marginTop: 16, paddingHorizontal: 16 },
  statBox: { flex: 1, backgroundColor: 'white', marginHorizontal: 6, borderRadius: 12, padding: 14, alignItems: 'center', shadowColor: '#000', shadowOpacity: 0.05, shadowRadius: 6, shadowOffset: { width: 0, height: 2 }, elevation: 2 },
  statNum: { fontWeight: '800', color: '#0F172A' },
  statLbl: { color: '#64748B', marginTop: 4 },
  section: { marginTop: 20, paddingHorizontal: 16 },
  sectionTitle: { fontWeight: '800', color: '#0F172A', marginBottom: 12 },
  actionsGrid: { flexDirection: 'row', flexWrap: 'wrap', gap: 10 as any },
  actionCard: { width: '48%', backgroundColor: 'white', padding: 14, borderRadius: 12, shadowColor: '#000', shadowOpacity: 0.05, shadowRadius: 6, shadowOffset: { width: 0, height: 2 }, elevation: 2, flexDirection: 'row', alignItems: 'center', gap: 10 as any },
  actionText: { fontWeight: '700', color: '#0F172A' },
});