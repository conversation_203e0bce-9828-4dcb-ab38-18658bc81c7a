import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Home, Search, Calendar, MessageCircle, User } from 'lucide-react-native';

// Import screens
import HomeScreen from './Screens/HomeScreen';
import ExploreScreen from './Screens/ExploreScreen';
import BookingsScreen from './Screens/BookingsScreen';
import MessageScreen from './Screens/MessageScreen';
import ProfileScreen from './Screens/ProfileScreen';

const Tab = createBottomTabNavigator();

export default function App() {
  return (
    <SafeAreaProvider>
      <NavigationContainer>
        <StatusBar style="auto" />
        <Tab.Navigator
          screenOptions={({ route }) => ({
            tabBarIcon: ({ focused, color, size }) => {
              let IconComponent;

              if (route.name === 'Home') {
                IconComponent = Home;
              } else if (route.name === 'Explore') {
                IconComponent = Search;
              } else if (route.name === 'Bookings') {
                IconComponent = Calendar;
              } else if (route.name === 'Messages') {
                IconComponent = MessageCircle;
              } else if (route.name === 'Profile') {
                IconComponent = User;
              }

              return <IconComponent size={size} color={color} />;
            },
            tabBarActiveTintColor: '#6366f1',
            tabBarInactiveTintColor: '#9ca3af',
            headerShown: false,
            tabBarStyle: {
              backgroundColor: '#ffffff',
              borderTopWidth: 1,
              borderTopColor: '#f3f4f6',
              paddingBottom: 5,
              paddingTop: 5,
              height: 60,
            },
          })}
        >
          <Tab.Screen name="Home" component={HomeScreen} />
          <Tab.Screen name="Explore" component={ExploreScreen} />
          <Tab.Screen name="Bookings" component={BookingsScreen} />
          <Tab.Screen name="Messages" component={MessageScreen} />
          <Tab.Screen name="Profile" component={ProfileScreen} />
        </Tab.Navigator>
      </NavigationContainer>
    </SafeAreaProvider>
  );
}