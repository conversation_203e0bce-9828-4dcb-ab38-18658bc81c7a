import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { Home, Search, Calendar, MessageCircle, User } from 'lucide-react-native';

// Import screens
import HomeScreen from './Screens/HomeScreen';
import ExploreScreen from './Screens/ExploreScreen';
import BookingsScreen from './Screens/BookingsScreen';
import MessageScreen from './Screens/MessageScreen';
import ProfileScreen from './Screens/ProfileScreen';

// Import providers and services
import { ThemeProvider } from './services/ThemeContext';
import { AuthProvider } from './services/AuthContext';

const Tab = createBottomTabNavigator();

export default function App() {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaProvider>
        <ThemeProvider>
          <AuthProvider>
            <NavigationContainer>
              <StatusBar style="auto" />
              <Tab.Navigator
                screenOptions={({ route }) => ({
                  tabBarIcon: ({ focused, color, size }) => {
                    let IconComponent;

                    if (route.name === 'Home') {
                      IconComponent = Home;
                    } else if (route.name === 'Explore') {
                      IconComponent = Search;
                    } else if (route.name === 'Bookings') {
                      IconComponent = Calendar;
                    } else if (route.name === 'Messages') {
                      IconComponent = MessageCircle;
                    } else if (route.name === 'Profile') {
                      IconComponent = User;
                    }

                    return <IconComponent size={size} color={color} />;
                  },
                  tabBarActiveTintColor: '#007AFF',
                  tabBarInactiveTintColor: 'gray',
                  headerShown: false,
                })}
              >
                <Tab.Screen name="Home" component={HomeScreen} />
                <Tab.Screen name="Explore" component={ExploreScreen} />
                <Tab.Screen name="Bookings" component={BookingsScreen} />
                <Tab.Screen name="Messages" component={MessageScreen} />
                <Tab.Screen name="Profile" component={ProfileScreen} />
              </Tab.Navigator>
            </NavigationContainer>
          </AuthProvider>
        </ThemeProvider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}