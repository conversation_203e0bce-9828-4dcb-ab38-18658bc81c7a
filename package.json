{"name": "project", "version": "1.0.0", "license": "0BSD", "private": true, "main": "index.ts", "scripts": {"start": "npx expo start", "android": "npx expo run:android", "ios": "npx expo run:ios", "web": "npx expo start --web"}, "dependencies": {"@expo/vector-icons": "latest", "@react-navigation/bottom-tabs": "7.4.5", "@react-navigation/native": "7.1.17", "expo": "^52.0.46", "expo-linear-gradient": "14.1.5", "expo-status-bar": "~1.12.1", "lucide-react-native": "0.536.0", "react": "18.3.1", "react-native": "0.76.9", "react-native-gesture-handler": "^2.16.1", "react-native-safe-area-context": "^4.10.5", "react-native-screens": "4.11.1", "react-native-svg": "15.12.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "typescript": "^5.1.3"}}