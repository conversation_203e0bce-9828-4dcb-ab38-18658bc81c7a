{"name": "project", "version": "1.0.0", "license": "0BSD", "private": true, "main": "index.ts", "scripts": {"start": "npx expo start", "android": "npx expo run:android", "ios": "npx expo run:ios", "web": "npx expo start --web"}, "dependencies": {"@auth0/auth0-react": "2.0.1", "@clerk/clerk-react": "5.0.0", "@expo/vector-icons": "latest", "@react-native-async-storage/async-storage": "latest", "@react-native-community/slider": "4.5.7", "@react-navigation/bottom-tabs": "7.4.5", "@react-navigation/native": "7.1.17", "@react-navigation/native-stack": "7.3.24", "@supabase/supabase-js": "2.53.0", "convex": "1.25.4", "convex/server": "1.25.4", "convex/values": "1.25.4", "expo": "^52.0.46", "expo-camera": "16.1.11", "expo-linear-gradient": "14.1.5", "lucide-react-native": "0.536.0", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-gesture-handler": "^2.16.1", "react-native-reanimated": "^3.10.1", "react-native-safe-area-context": "^4.10.5", "react-native-screens": "4.11.1", "react-native-svg": "15.12.1", "react-native-web": "~0.19.6", "sonner-native": "0.21.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "typescript": "^5.1.3"}}