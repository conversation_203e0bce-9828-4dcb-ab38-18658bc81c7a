const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Add support for additional file extensions
config.resolver.assetExts.push('db', 'mp3', 'ttf', 'obj', 'png', 'jpg');

// Add support for TypeScript paths
config.resolver.alias = {
  '@': __dirname,
  '@components': `${__dirname}/components`,
  '@screens': `${__dirname}/Screens`,
  '@services': `${__dirname}/services`,
  '@utils': `${__dirname}/utils`,
  '@types': `${__dirname}/types`,
  '@constants': `${__dirname}/constants`,
};

module.exports = config;
