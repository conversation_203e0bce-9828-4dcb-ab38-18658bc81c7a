// Navigation types
export type RootTabParamList = {
  Home: undefined;
  Explore: undefined;
  Bookings: undefined;
  Messages: undefined;
  Profile: undefined;
};

// User types
export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'client' | 'freelancer';
  rating?: number;
  reviewCount?: number;
}

// Booking types
export interface Booking {
  id: string;
  title: string;
  client: User;
  freelancer: User;
  date: string;
  time: string;
  duration: string;
  location: string;
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
  price: number;
  description?: string;
  rating?: number;
}

// Message types
export interface Message {
  id: string;
  senderId: string;
  receiverId: string;
  content: string;
  timestamp: string;
  read: boolean;
}

export interface Conversation {
  id: string;
  participants: User[];
  lastMessage: Message;
  unreadCount: number;
}

// Service types
export interface Service {
  id: string;
  title: string;
  description: string;
  category: string;
  price: number;
  duration: string;
  freelancer: User;
  images: string[];
  rating: number;
  reviewCount: number;
}

// Common types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}
