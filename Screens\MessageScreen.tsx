import React from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const MOCK_THREADS = [
  { id: 't1', name: '<PERSON>', last: 'Can you do Saturday?', unread: 2 },
  { id: 't2', name: '<PERSON>', last: 'Sharing sample shots now', unread: 0 },
  { id: 't3', name: '<PERSON>', last: 'Rate confirmed. Thanks!', unread: 1 },
];

export default function MessagesScreen() {
  return (
    <View style={styles.container}>
      <Text style={styles.header}>Messages</Text>
      <FlatList
        data={MOCK_THREADS}
        keyExtractor={(i) => i.id}
        contentContainerStyle={{ padding: 16 }}
        renderItem={({ item }) => (
          <TouchableOpacity style={styles.row}>
            <Ionicons name="person-circle" size={44} color="#9CA3AF" />
            <View style={{ marginLeft: 12, flex: 1 }}>
              <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text style={styles.name}>{item.name}</Text>
                {item.unread > 0 && (
                  <View style={styles.badge}><Text style={styles.badgeText}>{item.unread}</Text></View>
                )}
              </View>
              <Text style={styles.last} numberOfLines={1}>{item.last}</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#94A3B8" />
          </TouchableOpacity>
        )}
        ListEmptyComponent={() => (
          <View style={{ alignItems: 'center', padding: 24 }}>
            <Ionicons name="chatbubbles-outline" size={28} color="#94A3B8" />
            <Text style={{ color: '#94A3B8', marginTop: 8 }}>No messages yet</Text>
          </View>
        )}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  header: { fontSize: 22, fontWeight: '700', marginTop: 8, paddingHorizontal: 16, color: '#0F172A' },
  row: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 12,
    marginBottom: 10,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 6,
    shadowOffset: { width: 0, height: 2 },
    elevation: 2,
  },
  name: { fontWeight: '700', color: '#0F172A' },
  last: { color: '#64748B', marginTop: 2 },
  badge: { backgroundColor: '#0EA5E9', borderRadius: 999, paddingHorizontal: 8, paddingVertical: 2 },
  badgeText: { color: 'white', fontWeight: '700', fontSize: 12 },
});