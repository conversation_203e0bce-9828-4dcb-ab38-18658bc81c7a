import React from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const BOOKINGS = [
  { id: 'b1', name: 'Website Revamp', with: '<PERSON>', date: 'Aug 12, 3:00 PM', status: 'Pending' },
  { id: 'b2', name: 'Portrait Shoot', with: '<PERSON>', date: 'Aug 18, 11:00 AM', status: 'Confirmed' },
  { id: 'b3', name: 'App Prototype', with: '<PERSON>', date: 'Aug 22, 9:30 AM', status: 'Completed' },
];

function StatusPill({ status }: { status: string }) {
  const map: Record<string, { bg: string; fg: string }> = {
    Pending: { bg: '#FEF3C7', fg: '#92400E' },
    Confirmed: { bg: '#DCFCE7', fg: '#065F46' },
    Completed: { bg: '#E5E7EB', fg: '#374151' },
  };
  const s = map[status] || { bg: '#E5E7EB', fg: '#374151' };
  return (
    <View style={{ backgroundColor: s.bg, paddingHorizontal: 10, paddingVertical: 4, borderRadius: 999 }}>
      <Text style={{ color: s.fg, fontWeight: '700', fontSize: 12 }}>{status}</Text>
    </View>
  );
}

export default function BookingsScreen() {
  return (
    <View style={styles.container}>
      <Text style={styles.header}>Bookings</Text>
      <FlatList
        data={BOOKINGS}
        keyExtractor={(i) => i.id}
        contentContainerStyle={{ padding: 16 }}
        renderItem={({ item }) => (
          <View style={styles.card}>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Ionicons name="calendar" size={40} color="#60A5FA" />
              <View style={{ marginLeft: 12, flex: 1 }}>
                <Text style={styles.title}>{item.name}</Text>
                <Text style={styles.sub}>with {item.with} · {item.date}</Text>
              </View>
              <StatusPill status={item.status} />
            </View>
            <View style={styles.actions}>
              <TouchableOpacity style={[styles.actionBtn, { backgroundColor: '#E0F2FE' }]}>
                <Ionicons name="chatbubble-ellipses" size={16} color="#0369A1" />
                <Text style={[styles.actionText, { color: '#0369A1' }]}>Message</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.primaryBtn}>
                <Text style={styles.primaryText}>View</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
        ListEmptyComponent={() => (
          <View style={{ alignItems: 'center', padding: 24 }}>
            <Ionicons name="calendar-outline" size={28} color="#94A3B8" />
            <Text style={{ color: '#94A3B8', marginTop: 8 }}>No bookings yet</Text>
          </View>
        )}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  header: { fontSize: 22, fontWeight: '700', marginTop: 8, paddingHorizontal: 16, color: '#0F172A' },
  card: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 14,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOpacity: 0.06,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    elevation: 2,
  },
  title: { fontWeight: '700', color: '#0F172A', fontSize: 16 },
  sub: { color: '#64748B', marginTop: 2 },
  actions: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginTop: 12 },
  actionBtn: { flexDirection: 'row', alignItems: 'center', gap: 6 as any, paddingHorizontal: 12, paddingVertical: 8, borderRadius: 10 },
  actionText: { fontWeight: '700' },
  primaryBtn: { backgroundColor: '#0EA5E9', paddingHorizontal: 16, paddingVertical: 10, borderRadius: 10 },
  primaryText: { color: 'white', fontWeight: '700' },
});